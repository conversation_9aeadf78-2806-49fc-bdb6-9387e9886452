{"name": "nukeisreal-backend", "version": "1.0.0", "description": "Backend for NukeIsreal donation website with Solana Web3 integration", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["solana", "web3", "donation", "phantom-wallet"], "author": "", "license": "MIT"}