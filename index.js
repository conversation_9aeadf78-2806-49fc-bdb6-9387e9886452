const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: 'http://localhost:3000', // React dev server
  credentials: true
}));
app.use(express.json());

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'NukeIsreal Backend API is running!' });
});

// Wallet validation endpoint
app.post('/api/validate-wallet', (req, res) => {
  try {
    const { wallet } = req.body;
    
    // Basic validation
    if (!wallet) {
      return res.status(400).json({ 
        success: false, 
        error: 'Wallet address is required' 
      });
    }
    
    // Basic Solana address format validation (44 characters, base58)
    if (typeof wallet !== 'string' || wallet.length < 32 || wallet.length > 44) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid wallet address format' 
      });
    }
    
    console.log(`Wallet validation request for: ${wallet}`);
    
    // For now, just return success
    // Later we'll add signature verification
    res.json({ 
      success: true, 
      message: 'Wallet validated successfully',
      wallet: wallet
    });
    
  } catch (error) {
    console.error('Wallet validation error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    success: false, 
    error: 'Something went wrong!' 
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'Route not found' 
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📡 CORS enabled for http://localhost:3000`);
});
